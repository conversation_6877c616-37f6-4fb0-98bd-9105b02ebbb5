import { Ionicons } from '@expo/vector-icons';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { StyleSheet, View, Animated, TouchableOpacity } from 'react-native';

// Import screens
import ActivityDetailScreen from '../screens/ActivityDetailScreen';
import CalendarScreen from '../screens/CalendarScreen';
import GoalScreen from '../screens/GoalScreen';
import HomeScreen from '../screens/HomeScreen';
import NewActivityScreen from '../screens/NewActivityScreen';
import SettingsScreen from '../screens/SettingsScreen';

import { BottomTabParamList, RootStackParamList } from '../types';
import { getSemanticColors, spacing } from '../styles';
import { useTheme } from '../components';

const Tab = createBottomTabNavigator<BottomTabParamList>();
const Stack = createStackNavigator<RootStackParamList>();

// Modern custom tab bar component with animations
const CustomTabBar = ({ state, descriptors, navigation }: any) => {
  const { theme } = useTheme();
  const colors = getSemanticColors(theme);
  const animatedValues = React.useRef(
    state.routes.map(() => new Animated.Value(0))
  ).current;

  React.useEffect(() => {
    animatedValues.forEach((animValue, index) => {
      Animated.timing(animValue, {
        toValue: state.index === index ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    });
  }, [state.index, animatedValues]);

  return (
    <View style={[styles.tabBar, { backgroundColor: colors.surface }]}>
      {state.routes.map((route: any, index: number) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        let iconName: keyof typeof Ionicons.glyphMap;
        let iconNameFocused: keyof typeof Ionicons.glyphMap;

        switch (route.name) {
          case 'Calendar':
            iconName = 'calendar-outline';
            iconNameFocused = 'calendar';
            break;
          case 'Home':
            iconName = 'home-outline';
            iconNameFocused = 'home';
            break;
          case 'Settings':
            iconName = 'person-circle-outline';
            iconNameFocused = 'person-circle';
            break;
          default:
            iconName = 'home-outline';
            iconNameFocused = 'home';
        }

        const animatedIconColor = animatedValues[index].interpolate({
          inputRange: [0, 1],
          outputRange: [colors.textSecondary, colors.primary],
        });

        const animatedScale = animatedValues[index].interpolate({
          inputRange: [0, 1],
          outputRange: [1, 1.1],
        });

        return (
          <TouchableOpacity
            key={route.key}
            style={styles.tabItem}
            onPress={onPress}
            activeOpacity={0.7}
          >
            <Animated.View
              style={[
                styles.tabIconContainer,
                {
                  transform: [{ scale: animatedScale }],
                },
              ]}
            >
              <Animated.View
                style={[
                  styles.tabIconBackground,
                  {
                    backgroundColor: animatedValues[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: ['transparent', colors.primary + '20'],
                    }),
                  },
                ]}
              />
              <Ionicons
                name={isFocused ? iconNameFocused : iconName}
                size={24}
                color={isFocused ? colors.primary : colors.textSecondary}
              />
            </Animated.View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// Bottom tab navigator
function TabNavigator() {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen 
        name="Calendar" 
        component={CalendarScreen}
        options={{
          tabBarLabel: 'Calendar',
        }}
      />
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          tabBarLabel: 'Settings',
        }}
      />
    </Tab.Navigator>
  );
}

// Main stack navigator
export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="Main" component={TabNavigator} />
        <Stack.Screen 
          name="NewActivity" 
          component={NewActivityScreen}
          options={{
            presentation: 'modal',
            headerShown: true,
            headerTitle: 'New Activity',
          }}
        />
        <Stack.Screen 
          name="ActivityDetail" 
          component={ActivityDetailScreen}
          options={{
            presentation: 'modal',
            headerShown: true,
            headerTitle: 'Activity Detail',
          }}
        />
        <Stack.Screen 
          name="GoalView" 
          component={GoalScreen}
          options={{
            presentation: 'modal',
            headerShown: true,
            headerTitle: 'Goals',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: spacing.component.tabBarHeight,
    paddingBottom: spacing.component.safeAreaBottom,
    paddingTop: spacing['3'],
    borderTopWidth: 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    position: 'relative',
  },
  tabIconBackground: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
});
