// 基础类型定义
export interface Activity {
  id: string;
  tagName: string;
  tagType: 'pain' | 'gain';
  note?: string;
  point: number;
  tagId?: string;
  goalId?: string;
  type: 'task' | 'goal';
  createdAt: Date;
}

export interface Tag {
  id: string;
  name: string;
  type: 'pain' | 'gain';
  orderIndex: number;
  createdAt: Date;
}

export interface LongTermGoal {
  id: string;
  tagId: string;
  targetNo: number;
  currentNo: number;
  point: number;
  startTime: Date;
  endTime: Date;
  status: 'inprogress' | 'done' | 'cancelled';
  createdAt: Date;
}

export interface AppSetting {
  id: string;
  key: string;
  value: string;
}

export interface Visit {
  id: string;
  visitTime: Date;
  count: number;
}

export interface RemainingPoint {
  id: string;
  point: number;
  updatedAt: Date;
}

export interface Wish {
  id: string;
  name: string;
  point: number;
  note?: string;
  createdAt: Date;
}

// 枚举类型
export enum TagType {
  PAIN = 'pain',
  GAIN = 'gain'
}

export enum ActivityType {
  TASK = 'task',
  GOAL = 'goal'
}

export enum ViewType {
  HOME = 'home',
  CALENDAR = 'calendar',
  SETTING = 'setting'
}

// 导航类型
export type RootStackParamList = {
  Main: undefined;
  NewActivity: { actType: TagType };
  ActivityDetail: { activity: Activity };
  GoalView: undefined;
};

export type BottomTabParamList = {
  Home: undefined;
  Calendar: undefined;
  Settings: undefined;
};

// 数据库相关类型
export interface DatabaseActivity {
  id: string;
  tag_name: string;
  tag_type: string;
  note: string | null;
  point: number;
  tag_id: string | null;
  goal_id: string | null;
  type: string;
  created_at: string;
}

export interface DatabaseTag {
  id: string;
  name: string;
  type: string;
  order_index: number;
  created_at: string;
}

export interface DatabaseLongTermGoal {
  id: string;
  tag_id: string;
  target_no: number;
  current_no: number;
  point: number;
  start_time: string;
  end_time: string;
  status: string;
  created_at: string;
}

export interface DatabaseAppSetting {
  id: string;
  key: string;
  value: string;
}

export interface DatabaseVisit {
  id: string;
  visit_time: string;
  count: number;
}

export interface DatabaseRemainingPoint {
  id: string;
  point: number;
  updated_at: string;
}

export interface DatabaseWish {
  id: string;
  name: string;
  point: number;
  note: string | null;
  created_at: string;
}
