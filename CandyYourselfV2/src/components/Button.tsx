import React, { useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
  Animated,
  Haptics,
} from 'react-native';
import { lightColors, darkColors, getSemanticColors } from '../styles/colors';
import { textStyles } from '../styles/typography';
import { spacing } from '../styles/spacing';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
  theme?: 'light' | 'dark';
  hapticFeedback?: boolean;
  animationType?: 'scale' | 'bounce' | 'none';
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  theme = 'light',
  hapticFeedback = true,
  animationType = 'scale',
}) => {
  const colors = getSemanticColors(theme);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  
  const getButtonStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: spacing.component.radiusMD,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle.paddingVertical = spacing.component.buttonPaddingSmallVertical;
        baseStyle.paddingHorizontal = spacing.component.buttonPaddingSmallHorizontal;
        baseStyle.minHeight = 36;
        break;
      case 'large':
        baseStyle.paddingVertical = spacing.component.buttonPaddingLargeVertical;
        baseStyle.paddingHorizontal = spacing.component.buttonPaddingLargeHorizontal;
        baseStyle.minHeight = 52;
        break;
      default: // medium
        baseStyle.paddingVertical = spacing.component.buttonPaddingVertical;
        baseStyle.paddingHorizontal = spacing.component.buttonPaddingHorizontal;
        baseStyle.minHeight = 44;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = colors.primary;
        break;
      case 'secondary':
        baseStyle.backgroundColor = colors.secondary;
        break;
      case 'outline':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.primary;
        break;
      case 'ghost':
        baseStyle.backgroundColor = 'transparent';
        break;
      case 'danger':
        baseStyle.backgroundColor = colors.error;
        break;
    }

    // Disabled state
    if (disabled) {
      baseStyle.opacity = 0.5;
    }

    // Full width
    if (fullWidth) {
      baseStyle.width = '100%';
    }

    return baseStyle;
  };

  const getTextStyles = (): TextStyle => {
    let baseTextStyle: TextStyle;

    // Size-based text styles
    switch (size) {
      case 'small':
        baseTextStyle = textStyles.buttonSmall;
        break;
      case 'large':
        baseTextStyle = textStyles.buttonLarge;
        break;
      default:
        baseTextStyle = textStyles.button;
    }

    // Variant-based text colors
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        baseTextStyle.color = colors.textOnPrimary;
        break;
      case 'outline':
        baseTextStyle.color = colors.primary;
        break;
      case 'ghost':
        baseTextStyle.color = colors.text;
        break;
    }

    return baseTextStyle;
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={variant === 'outline' || variant === 'ghost' ? colors.primary : colors.textOnPrimary}
          />
          <Text style={[getTextStyles(), { marginLeft: spacing['2'] }, textStyle]}>
            {title}
          </Text>
        </View>
      );
    }

    if (icon) {
      return (
        <View style={styles.contentContainer}>
          {iconPosition === 'left' && (
            <View style={[styles.iconContainer, { marginRight: spacing['2'] }]}>
              {icon}
            </View>
          )}
          <Text style={[getTextStyles(), textStyle]}>{title}</Text>
          {iconPosition === 'right' && (
            <View style={[styles.iconContainer, { marginLeft: spacing['2'] }]}>
              {icon}
            </View>
          )}
        </View>
      );
    }

    return <Text style={[getTextStyles(), textStyle]}>{title}</Text>;
  };

  const handlePressIn = () => {
    if (animationType === 'scale') {
      Animated.spring(scaleAnim, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    } else if (animationType === 'bounce') {
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }).start();
    }

    Animated.timing(opacityAnim, {
      toValue: 0.8,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    if (animationType === 'scale') {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } else if (animationType === 'bounce') {
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }).start();
    }

    Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (hapticFeedback && !disabled && !loading) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress();
  };

  const getAnimatedStyle = () => {
    if (animationType === 'none') {
      return {};
    }

    return {
      transform: [{ scale: scaleAnim }],
      opacity: opacityAnim,
    };
  };

  return (
    <Animated.View style={[getAnimatedStyle(), style]}>
      <TouchableOpacity
        style={getButtonStyles()}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={1}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Button;
