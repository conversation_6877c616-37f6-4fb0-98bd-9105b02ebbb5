import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Animated,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { getSemanticColors } from '../styles/colors';
import { spacing } from '../styles/spacing';

interface FloatingActionButtonProps {
  children: React.ReactNode;
  onPress: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'primary' | 'secondary' | 'candy';
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  disabled?: boolean;
  style?: ViewStyle;
  theme?: 'light' | 'dark';
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  children,
  onPress,
  size = 'medium',
  variant = 'candy',
  position = 'bottom-right',
  disabled = false,
  style,
  theme = 'light',
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const shadowAnim = useRef(new Animated.Value(0.3)).current;
  const colors = getSemanticColors(theme);

  useEffect(() => {
    // Breathing animation
    const breathingAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );

    const shadowAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shadowAnim, {
          toValue: 0.4,
          duration: 2000,
          useNativeDriver: false,
        }),
        Animated.timing(shadowAnim, {
          toValue: 0.3,
          duration: 2000,
          useNativeDriver: false,
        }),
      ])
    );

    if (!disabled) {
      breathingAnimation.start();
      shadowAnimation.start();
    }

    return () => {
      breathingAnimation.stop();
      shadowAnimation.stop();
    };
  }, [disabled, scaleAnim, shadowAnim]);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return 48;
      case 'large':
        return 64;
      default:
        return 56;
    }
  };

  const getButtonStyles = (): ViewStyle => {
    const buttonSize = getButtonSize();
    const baseStyle: ViewStyle = {
      width: buttonSize,
      height: buttonSize,
      borderRadius: buttonSize / 2,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
    };

    // Position styles
    switch (position) {
      case 'bottom-left':
        baseStyle.bottom = spacing.component.tabBarHeight + spacing['4'];
        baseStyle.left = spacing.layout.screenPadding;
        break;
      case 'bottom-center':
        baseStyle.bottom = spacing.component.tabBarHeight + spacing['4'];
        baseStyle.alignSelf = 'center';
        break;
      default: // bottom-right
        baseStyle.bottom = spacing.component.tabBarHeight + spacing['4'];
        baseStyle.right = spacing.layout.screenPadding;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = colors.primary;
        break;
      case 'secondary':
        baseStyle.backgroundColor = colors.secondary;
        break;
      case 'candy':
        baseStyle.backgroundColor = '#FFB6C1'; // Light pink candy color
        break;
    }

    // Disabled state
    if (disabled) {
      baseStyle.opacity = 0.5;
    }

    return baseStyle;
  };

  return (
    <Animated.View
      style={[
        getButtonStyles(),
        {
          transform: [{ scale: scaleAnim }],
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: shadowAnim,
          shadowRadius: 8,
          elevation: 8,
        },
        style,
      ]}
    >
      <TouchableOpacity
        style={styles.touchable}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  touchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default FloatingActionButton;
