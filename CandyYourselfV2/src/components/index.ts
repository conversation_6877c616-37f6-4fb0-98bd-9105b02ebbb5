// Modern UI Components Library
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { <PERSON>, CardHeader, CardContent, CardFooter } from './Card';
export type { CardProps } from './Card';

export { Input } from './Input';
export type { InputProps } from './Input';

export { GradientBackground } from './GradientBackground';

export { FloatingActionButton } from './FloatingActionButton';

export { ThemeProvider, useTheme } from './ThemeProvider';

export { LoadingSpinner, CandyLoadingSpinner } from './LoadingSpinner';

export { AnimatedScreen, StaggeredAnimation, PageTransition } from './AnimatedScreen';

// Re-export common types
export type { Theme } from '../styles/colors';
