import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Theme, setTheme } from '../styles/colors';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isSystemTheme: boolean;
  setSystemTheme: (useSystem: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@candy_yourself_theme';
const SYSTEM_THEME_STORAGE_KEY = '@candy_yourself_system_theme';

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setCurrentTheme] = useState<Theme>('light');
  const [isSystemTheme, setIsSystemTheme] = useState(true);

  // Load saved theme preference on mount
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Listen to system theme changes
  useEffect(() => {
    if (isSystemTheme) {
      const subscription = Appearance.addChangeListener(({ colorScheme }) => {
        if (colorScheme) {
          updateTheme(colorScheme === 'dark' ? 'dark' : 'light');
        }
      });

      // Set initial system theme
      const systemTheme = Appearance.getColorScheme();
      if (systemTheme) {
        updateTheme(systemTheme === 'dark' ? 'dark' : 'light');
      }

      return () => subscription?.remove();
    }
  }, [isSystemTheme]);

  const loadThemePreference = async () => {
    try {
      const [savedTheme, savedSystemTheme] = await Promise.all([
        AsyncStorage.getItem(THEME_STORAGE_KEY),
        AsyncStorage.getItem(SYSTEM_THEME_STORAGE_KEY),
      ]);

      const useSystemTheme = savedSystemTheme !== null ? JSON.parse(savedSystemTheme) : true;
      setIsSystemTheme(useSystemTheme);

      if (useSystemTheme) {
        const systemTheme = Appearance.getColorScheme();
        updateTheme(systemTheme === 'dark' ? 'dark' : 'light');
      } else if (savedTheme) {
        updateTheme(savedTheme as Theme);
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  const updateTheme = (newTheme: Theme) => {
    setCurrentTheme(newTheme);
    setTheme(newTheme); // Update global theme
  };

  const handleSetTheme = async (newTheme: Theme) => {
    try {
      setIsSystemTheme(false);
      updateTheme(newTheme);
      
      await Promise.all([
        AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme),
        AsyncStorage.setItem(SYSTEM_THEME_STORAGE_KEY, JSON.stringify(false)),
      ]);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    handleSetTheme(newTheme);
  };

  const setSystemTheme = async (useSystem: boolean) => {
    try {
      setIsSystemTheme(useSystem);
      await AsyncStorage.setItem(SYSTEM_THEME_STORAGE_KEY, JSON.stringify(useSystem));

      if (useSystem) {
        const systemTheme = Appearance.getColorScheme();
        if (systemTheme) {
          updateTheme(systemTheme === 'dark' ? 'dark' : 'light');
        }
      }
    } catch (error) {
      console.error('Error saving system theme preference:', error);
    }
  };

  const value: ThemeContextType = {
    theme,
    setTheme: handleSetTheme,
    toggleTheme,
    isSystemTheme,
    setSystemTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeProvider;
