import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { getSemanticColors } from '../styles/colors';

interface GradientBackgroundProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'candy' | 'sunset' | 'ocean';
  style?: ViewStyle;
  theme?: 'light' | 'dark';
}

export const GradientBackground: React.FC<GradientBackgroundProps> = ({
  children,
  variant = 'candy',
  style,
  theme = 'light',
}) => {
  const colors = getSemanticColors(theme);

  const getGradientColors = () => {
    switch (variant) {
      case 'primary':
        return [colors.primary, colors.primaryVariant];
      case 'secondary':
        return [colors.secondary, colors.secondaryVariant];
      case 'candy':
        // Candy-inspired gradient
        return theme === 'light' 
          ? ['#FFE5F1', '#F0F8FF', '#E5F3FF'] 
          : ['#2D1B2F', '#1E2A3A', '#1A2332'];
      case 'sunset':
        return theme === 'light'
          ? ['#FF9A9E', '#FECFEF', '#FECFEF']
          : ['#4A1A2C', '#2D1B3D', '#1E1A3A'];
      case 'ocean':
        return theme === 'light'
          ? ['#A8E6CF', '#88D8C0', '#7FCDCD']
          : ['#1A3A2E', '#16302B', '#132A26'];
      default:
        return [colors.background, colors.backgroundSecondary];
    }
  };

  return (
    <LinearGradient
      colors={getGradientColors()}
      style={[styles.container, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default GradientBackground;
