import { Activity, ActivityType, LongTermGoal, Tag, TagType } from '../types';
import { database } from './Database';

export class DataSource {
  private activities: Activity[] = [];
  private tags: Tag[] = [];
  private goals: LongTermGoal[] = [];
  private balance: number = 0;
  private visitCount: number = 0;
  private isVIP: boolean = false;
  private currentOffset: number = 0;
  private canLoadMorePages: boolean = true;

  // Listeners for state changes
  private listeners: Set<() => void> = new Set();

  private initialized = false;

  constructor() {
    // Don't call async init in constructor
  }

  async init() {
    if (this.initialized) return;

    try {
      await database.init();
      await this.loadInitialData();
      this.initialized = true;
      console.log('DataSource initialized successfully');
    } catch (error) {
      console.error('Failed to initialize DataSource:', error);
      throw error;
    }
  }

  private async loadInitialData() {
    await this.updateBalance();
    await this.loadMoreContent();
    await this.loadTags();
    await this.loadGoals();
    await this.getAppSettings();
    this.notifyListeners();
  }

  // Listener management
  addListener(listener: () => void) {
    this.listeners.add(listener);
  }

  removeListener(listener: () => void) {
    this.listeners.delete(listener);
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener());
  }

  // Getters
  getActivities(): Activity[] {
    return this.activities;
  }

  getTags(): Tag[] {
    return this.tags;
  }

  getGoals(): LongTermGoal[] {
    return this.goals;
  }

  getBalance(): number {
    return this.balance;
  }

  getVisitCount(): number {
    return this.visitCount;
  }

  getIsVIP(): boolean {
    return this.isVIP;
  }

  // Activity management
  async loadMoreContent() {
    if (!this.canLoadMorePages) return;

    try {
      const newActivities = await database.getActivities(50, this.currentOffset);
      
      if (newActivities.length === 0) {
        this.canLoadMorePages = false;
        return;
      }

      this.activities = [...this.activities, ...newActivities];
      this.currentOffset += newActivities.length;
      this.notifyListeners();
    } catch (error) {
      console.error('Error loading activities:', error);
    }
  }

  async clearAndReload() {
    this.activities = [];
    this.currentOffset = 0;
    this.canLoadMorePages = true;
    await this.loadMoreContent();
  }

  async addActivity(activity: Activity) {
    try {
      await database.insertActivity(activity);
      this.activities.unshift(activity);
      await this.actionActivity(activity);
      this.notifyListeners();
    } catch (error) {
      console.error('Error adding activity:', error);
    }
  }

  async deleteActivity(activity: Activity) {
    try {
      await database.deleteActivity(activity.id);
      this.activities = this.activities.filter(a => a.id !== activity.id);
      await this.revertActivity(activity);
      this.notifyListeners();
    } catch (error) {
      console.error('Error deleting activity:', error);
    }
  }

  async getActivitiesByDate(date: Date): Promise<Activity[]> {
    try {
      return await database.getActivitiesByDate(date);
    } catch (error) {
      console.error('Error getting activities by date:', error);
      return [];
    }
  }

  // Tag management
  async loadTags() {
    try {
      this.tags = await database.getTags();
      this.notifyListeners();
    } catch (error) {
      console.error('Error loading tags:', error);
    }
  }

  async addTag(tag: Tag) {
    try {
      await database.insertTag(tag);
      this.tags.unshift(tag);
      this.notifyListeners();
    } catch (error) {
      console.error('Error adding tag:', error);
    }
  }

  async deleteTag(tag: Tag) {
    try {
      await database.deleteTag(tag.id);
      this.tags = this.tags.filter(t => t.id !== tag.id);
      this.notifyListeners();
    } catch (error) {
      console.error('Error deleting tag:', error);
    }
  }

  getTagsByType(type: TagType): Tag[] {
    return this.tags.filter(tag => tag.type === type);
  }

  getTagNameById(tagId: string): string {
    const tag = this.tags.find(t => t.id === tagId);
    return tag?.name || 'Unknown Tag';
  }

  // Goal management
  async loadGoals() {
    try {
      this.goals = await database.getGoals();
      this.notifyListeners();
    } catch (error) {
      console.error('Error loading goals:', error);
    }
  }

  async addGoal(goal: LongTermGoal) {
    try {
      // Check existing activities that match this goal
      for (const activity of this.activities) {
        if (activity.tagId === goal.tagId &&
            activity.createdAt >= goal.startTime &&
            activity.createdAt <= goal.endTime) {
          goal.currentNo += 1;
        }
      }

      // Check if goal is already completed
      if (goal.currentNo >= goal.targetNo) {
        goal.status = 'done';
        
        // Create goal completion activity
        const goalActivity: Activity = {
          id: Date.now().toString(),
          tagName: `${goal.targetNo} x ${this.getTagNameById(goal.tagId)}`,
          tagType: TagType.PAIN,
          point: goal.point,
          type: ActivityType.GOAL,
          goalId: goal.id,
          createdAt: new Date()
        };

        await this.addActivity(goalActivity);
      }

      await database.insertGoal(goal);
      this.goals.push(goal);
      this.notifyListeners();
    } catch (error) {
      console.error('Error adding goal:', error);
    }
  }

  async updateGoal(goal: LongTermGoal) {
    try {
      await database.updateGoal(goal);
      const index = this.goals.findIndex(g => g.id === goal.id);
      if (index !== -1) {
        this.goals[index] = goal;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('Error updating goal:', error);
    }
  }

  async deleteGoal(goal: LongTermGoal) {
    try {
      await database.deleteGoal(goal.id);
      this.goals = this.goals.filter(g => g.id !== goal.id);
      this.notifyListeners();
    } catch (error) {
      console.error('Error deleting goal:', error);
    }
  }

  async triggerLongTermGoals() {
    for (const goal of this.goals) {
      if (goal.status === 'inprogress') {
        let currentCount = 0;
        
        // Count matching activities
        for (const activity of this.activities) {
          if (activity.tagId === goal.tagId &&
              activity.createdAt >= goal.startTime &&
              activity.createdAt <= goal.endTime) {
            currentCount++;
          }
        }

        if (currentCount !== goal.currentNo) {
          goal.currentNo = currentCount;
          
          if (goal.currentNo >= goal.targetNo && goal.status === 'inprogress') {
            goal.status = 'done';
            
            // Create goal completion activity
            const goalActivity: Activity = {
              id: Date.now().toString(),
              tagName: `${goal.targetNo} x ${this.getTagNameById(goal.tagId)}`,
              tagType: TagType.PAIN,
              point: goal.point,
              type: ActivityType.GOAL,
              goalId: goal.id,
              createdAt: new Date()
            };

            await this.addActivity(goalActivity);
          }
          
          await this.updateGoal(goal);
        }
      }
    }
  }

  // Balance management
  async updateBalance() {
    try {
      this.balance = await database.getBalance();
      this.notifyListeners();
    } catch (error) {
      console.error('Error updating balance:', error);
    }
  }

  private async actionActivity(activity: Activity) {
    let pointChange = 0;
    
    if (activity.tagType === TagType.PAIN) {
      pointChange = activity.point; // Add points for pain activities
    } else if (activity.tagType === TagType.GAIN) {
      pointChange = -activity.point; // Subtract points for gain activities
    }

    const newBalance = this.balance + pointChange;
    await database.updateBalance(newBalance);
    this.balance = newBalance;
  }

  private async revertActivity(activity: Activity) {
    let pointChange = 0;
    
    if (activity.tagType === TagType.PAIN) {
      pointChange = -activity.point; // Subtract points when removing pain activities
    } else if (activity.tagType === TagType.GAIN) {
      pointChange = activity.point; // Add points back when removing gain activities
    }

    const newBalance = this.balance + pointChange;
    await database.updateBalance(newBalance);
    this.balance = newBalance;
  }

  // Visit tracking
  async getAndUpdateVisitTime(): Promise<number> {
    try {
      this.visitCount = await database.updateVisitCount();
      this.notifyListeners();
      return this.visitCount;
    } catch (error) {
      console.error('Error updating visit count:', error);
      return 0;
    }
  }

  // App settings
  async getAppSettings() {
    try {
      const vipSetting = await database.getSetting('isVIP');
      this.isVIP = vipSetting === 'true';
      this.notifyListeners();
    } catch (error) {
      console.error('Error getting app settings:', error);
    }
  }

  async setVIPStatus(isVIP: boolean) {
    try {
      await database.setSetting('isVIP', isVIP.toString());
      this.isVIP = isVIP;
      this.notifyListeners();
    } catch (error) {
      console.error('Error setting VIP status:', error);
    }
  }

  // Utility methods
  groupActivitiesByDate(activities: Activity[]): { [date: string]: Activity[] } {
    const grouped: { [date: string]: Activity[] } = {};
    
    activities.forEach(activity => {
      const dateKey = activity.createdAt.toISOString().split('T')[0];
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(activity);
    });

    return grouped;
  }

  getListModifier(activity: Activity): { backgroundColor: string; borderColor: string } {
    if (activity.tagType === TagType.PAIN) {
      return {
        backgroundColor: '#ffebee',
        borderColor: '#f44336'
      };
    } else {
      return {
        backgroundColor: '#e8f5e8',
        borderColor: '#4caf50'
      };
    }
  }
}

export const dataSource = new DataSource();
