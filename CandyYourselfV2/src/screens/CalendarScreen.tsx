import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';

import { dataSource } from '../database/DataSource';
import { Activity, TagType } from '../types';
import { colors, commonStyles, textStyles, spacing } from '../styles';
import { formatDate, formatDateForDisplay } from '../utils';

const CalendarScreen: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<string>(formatDate(new Date()));
  const [activitiesForDate, setActivitiesForDate] = useState<Activity[]>([]);
  const [markedDates, setMarkedDates] = useState<{ [key: string]: any }>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    loadCalendarData();
  }, []);

  useEffect(() => {
    loadActivitiesForDate(selectedDate);
  }, [selectedDate]);

  const loadCalendarData = async () => {
    try {
      const activities = dataSource.getActivities();
      const marked: { [key: string]: any } = {};

      // Group activities by date and mark calendar
      activities.forEach(activity => {
        const dateKey = formatDate(activity.createdAt);
        if (!marked[dateKey]) {
          marked[dateKey] = {
            marked: true,
            dotColor: activity.tagType === TagType.PAIN ? colors.pain.border : colors.gain.border,
            dots: []
          };
        }
        
        // Add dot for each activity type
        const existingDot = marked[dateKey].dots?.find((dot: any) => 
          dot.color === (activity.tagType === TagType.PAIN ? colors.pain.border : colors.gain.border)
        );
        
        if (!existingDot) {
          if (!marked[dateKey].dots) marked[dateKey].dots = [];
          marked[dateKey].dots.push({
            color: activity.tagType === TagType.PAIN ? colors.pain.border : colors.gain.border
          });
        }
      });

      // Mark selected date
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: colors.primary,
      };

      setMarkedDates(marked);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading calendar data:', error);
      setIsLoading(false);
    }
  };

  const loadActivitiesForDate = async (date: string) => {
    try {
      const activities = await dataSource.getActivitiesByDate(new Date(date));
      setActivitiesForDate(activities);
    } catch (error) {
      console.error('Error loading activities for date:', error);
      setActivitiesForDate([]);
    }
  };

  const onDayPress = (day: DateData) => {
    const newSelectedDate = day.dateString;
    setSelectedDate(newSelectedDate);
    
    // Update marked dates to show new selection
    const newMarkedDates = { ...markedDates };
    
    // Remove selection from previous date
    Object.keys(newMarkedDates).forEach(date => {
      if (newMarkedDates[date].selected) {
        delete newMarkedDates[date].selected;
        delete newMarkedDates[date].selectedColor;
      }
    });
    
    // Add selection to new date
    newMarkedDates[newSelectedDate] = {
      ...newMarkedDates[newSelectedDate],
      selected: true,
      selectedColor: colors.primary,
    };
    
    setMarkedDates(newMarkedDates);
  };

  const renderActivityItem = (activity: Activity) => (
    <View
      key={activity.id}
      style={[
        styles.activityItem,
        activity.tagType === TagType.PAIN ? commonStyles.activityPain : commonStyles.activityGain
      ]}
    >
      <View style={styles.activityContent}>
        <Text style={styles.activityName}>{activity.tagName}</Text>
        <Text style={styles.activityPoints}>
          {activity.tagType === TagType.PAIN ? '+' : '-'}{activity.point}
        </Text>
      </View>
      {activity.note && (
        <Text style={styles.activityNote}>{activity.note}</Text>
      )}
      <Text style={styles.activityTime}>
        {activity.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>
    </View>
  );

  const calculateDayStats = (activities: Activity[]) => {
    const painPoints = activities
      .filter(a => a.tagType === TagType.PAIN)
      .reduce((sum, a) => sum + a.point, 0);
    
    const gainPoints = activities
      .filter(a => a.tagType === TagType.GAIN)
      .reduce((sum, a) => sum + a.point, 0);
    
    return {
      painPoints,
      gainPoints,
      netPoints: painPoints - gainPoints,
      totalActivities: activities.length
    };
  };

  if (isLoading) {
    return (
      <View style={[commonStyles.container, commonStyles.flexCenter]}>
        <Text>Loading calendar...</Text>
      </View>
    );
  }

  const dayStats = calculateDayStats(activitiesForDate);

  return (
    <View style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Activity Calendar</Text>
      </View>

      {/* Calendar */}
      <Calendar
        current={selectedDate}
        onDayPress={onDayPress}
        markedDates={markedDates}
        markingType="multi-dot"
        theme={{
          backgroundColor: colors.background,
          calendarBackground: colors.background,
          textSectionTitleColor: colors.textSecondary,
          selectedDayBackgroundColor: colors.primary,
          selectedDayTextColor: colors.textLight,
          todayTextColor: colors.primary,
          dayTextColor: colors.text,
          textDisabledColor: colors.textTertiary,
          dotColor: colors.primary,
          selectedDotColor: colors.textLight,
          arrowColor: colors.primary,
          disabledArrowColor: colors.textTertiary,
          monthTextColor: colors.text,
          indicatorColor: colors.primary,
          textDayFontFamily: 'System',
          textMonthFontFamily: 'System',
          textDayHeaderFontFamily: 'System',
          textDayFontSize: 16,
          textMonthFontSize: 18,
          textDayHeaderFontSize: 14,
        }}
        style={styles.calendar}
      />

      {/* Selected date info */}
      <View style={styles.dateInfo}>
        <Text style={styles.selectedDateTitle}>
          {formatDateForDisplay(new Date(selectedDate))}
        </Text>
        
        {dayStats.totalActivities > 0 && (
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Tasks</Text>
              <Text style={[styles.statValue, { color: colors.pain.text }]}>
                +{dayStats.painPoints}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Rewards</Text>
              <Text style={[styles.statValue, { color: colors.gain.text }]}>
                -{dayStats.gainPoints}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Net</Text>
              <Text style={[
                styles.statValue,
                { color: dayStats.netPoints >= 0 ? colors.success : colors.error }
              ]}>
                {dayStats.netPoints >= 0 ? '+' : ''}{dayStats.netPoints}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Activities list */}
      <ScrollView
        style={styles.activitiesList}
        contentContainerStyle={styles.activitiesContent}
        showsVerticalScrollIndicator={false}
      >
        {activitiesForDate.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No activities on this date</Text>
          </View>
        ) : (
          activitiesForDate.map(renderActivityItem)
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingTop: spacing.component.safeAreaTop,
    paddingHorizontal: spacing.layout.screenPadding,
    paddingBottom: spacing.md,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerTitle: {
    ...textStyles.h3,
    color: colors.text,
    textAlign: 'center',
  },
  calendar: {
    marginHorizontal: spacing.layout.screenPadding,
    marginVertical: spacing.md,
    borderRadius: spacing.component.radiusMD,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dateInfo: {
    paddingHorizontal: spacing.layout.screenPadding,
    paddingVertical: spacing.md,
    backgroundColor: colors.backgroundSecondary,
    marginHorizontal: spacing.layout.screenPadding,
    borderRadius: spacing.component.radiusMD,
    marginBottom: spacing.md,
  },
  selectedDateTitle: {
    ...textStyles.h4,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    ...textStyles.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  statValue: {
    ...textStyles.h5,
    fontWeight: 'bold',
  },
  activitiesList: {
    flex: 1,
    paddingHorizontal: spacing.layout.screenPadding,
  },
  activitiesContent: {
    paddingBottom: spacing.component.tabBarHeight,
  },
  activityItem: {
    borderRadius: spacing.component.radiusMD,
    marginBottom: spacing.sm,
    padding: spacing.md,
  },
  activityContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  activityName: {
    ...textStyles.body,
    color: colors.text,
    flex: 1,
  },
  activityPoints: {
    ...textStyles.body,
    color: colors.text,
    fontWeight: 'bold',
  },
  activityNote: {
    ...textStyles.bodySmall,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    fontStyle: 'italic',
  },
  activityTime: {
    ...textStyles.caption,
    color: colors.textSecondary,
    textAlign: 'right',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing['2xl'],
  },
  emptyText: {
    ...textStyles.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default CalendarScreen;
