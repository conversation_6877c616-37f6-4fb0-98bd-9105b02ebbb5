import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import React from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { dataSource } from '../database/DataSource';
import { colors, commonStyles, spacing, textStyles } from '../styles';
import { RootStackParamList, TagType } from '../types';
import { formatDateForDisplay, getRelativeTime } from '../utils';

type ActivityDetailScreenRouteProp = RouteProp<RootStackParamList, 'ActivityDetail'>;

const ActivityDetailScreen: React.FC = () => {
  const route = useRoute<ActivityDetailScreenRouteProp>();
  const navigation = useNavigation();
  const { activity } = route.params;

  const handleDelete = () => {
    Alert.alert(
      'Delete Activity',
      'Are you sure you want to delete this activity? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await dataSource.deleteActivity(activity);
              Alert.alert('Success', 'Activity deleted successfully', [
                { text: 'OK', onPress: () => navigation.goBack() }
              ]);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete activity');
            }
          },
        },
      ]
    );
  };

  const getActivityTypeInfo = () => {
    if (activity.tagType === TagType.PAIN) {
      return {
        icon: 'checkmark-circle' as keyof typeof Ionicons.glyphMap,
        color: colors.pain.border,
        backgroundColor: colors.pain.background,
        typeLabel: 'Task Completed',
        pointsLabel: `+${activity.point} points earned`,
      };
    } else {
      return {
        icon: 'gift' as keyof typeof Ionicons.glyphMap,
        color: colors.gain.border,
        backgroundColor: colors.gain.background,
        typeLabel: 'Reward Redeemed',
        pointsLabel: `-${activity.point} points spent`,
      };
    }
  };

  const typeInfo = getActivityTypeInfo();

  return (
    <View style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Activity Details</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDelete}
        >
          <Ionicons name="trash-outline" size={24} color={colors.error} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Activity Card */}
        <View style={[styles.activityCard, { backgroundColor: typeInfo.backgroundColor }]}>
          <View style={styles.activityHeader}>
            <Ionicons name={typeInfo.icon} size={48} color={typeInfo.color} />
            <View style={styles.activityInfo}>
              <Text style={styles.activityType}>{typeInfo.typeLabel}</Text>
              <Text style={styles.activityName}>{activity.tagName}</Text>
            </View>
          </View>

          <View style={styles.pointsContainer}>
            <Text style={[styles.pointsText, { color: typeInfo.color }]}>
              {typeInfo.pointsLabel}
            </Text>
          </View>
        </View>

        {/* Details Section */}
        <View style={styles.detailsSection}>
          <Text style={styles.sectionTitle}>Details</Text>

          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={20} color={colors.textSecondary} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Date</Text>
              <Text style={styles.detailValue}>
                {formatDateForDisplay(activity.createdAt)}
              </Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={20} color={colors.textSecondary} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Time</Text>
              <Text style={styles.detailValue}>
                {activity.createdAt.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Ionicons name="hourglass-outline" size={20} color={colors.textSecondary} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Created</Text>
              <Text style={styles.detailValue}>
                {getRelativeTime(activity.createdAt)}
              </Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Ionicons name="pricetag-outline" size={20} color={colors.textSecondary} />
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Category</Text>
              <Text style={styles.detailValue}>
                {activity.tagType === TagType.PAIN ? 'Task' : 'Reward'}
              </Text>
            </View>
          </View>

          {activity.type && (
            <View style={styles.detailItem}>
              <Ionicons name="layers-outline" size={20} color={colors.textSecondary} />
              <View style={styles.detailContent}>
                <Text style={styles.detailLabel}>Type</Text>
                <Text style={styles.detailValue}>
                  {activity.type === 'goal' ? 'Goal Completion' : 'Regular Activity'}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Note Section */}
        {activity.note && (
          <View style={styles.noteSection}>
            <Text style={styles.sectionTitle}>Note</Text>
            <View style={styles.noteContainer}>
              <Text style={styles.noteText}>{activity.note}</Text>
            </View>
          </View>
        )}

        {/* Stats Section */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Impact</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{activity.point}</Text>
              <Text style={styles.statLabel}>Points</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {dataSource.getBalance()}
              </Text>
              <Text style={styles.statLabel}>Current Balance</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.layout.screenPadding,
    paddingTop: spacing.component.safeAreaTop,
    paddingBottom: spacing.md,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  backButton: {
    padding: spacing.sm,
  },
  title: {
    ...textStyles.h3,
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  deleteButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.layout.screenPadding,
  },
  activityCard: {
    borderRadius: spacing.component.radiusLG,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  activityInfo: {
    marginLeft: spacing.md,
    flex: 1,
  },
  activityType: {
    ...textStyles.bodySmall,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  activityName: {
    ...textStyles.h4,
    color: colors.text,
    marginTop: spacing.xs,
  },
  pointsContainer: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  pointsText: {
    ...textStyles.h5,
    fontWeight: 'bold',
  },
  detailsSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...textStyles.h5,
    color: colors.text,
    marginBottom: spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  detailContent: {
    marginLeft: spacing.md,
    flex: 1,
  },
  detailLabel: {
    ...textStyles.bodySmall,
    color: colors.textSecondary,
  },
  detailValue: {
    ...textStyles.body,
    color: colors.text,
    marginTop: spacing.xs,
  },
  noteSection: {
    marginBottom: spacing.lg,
  },
  noteContainer: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: spacing.component.radiusMD,
    padding: spacing.md,
  },
  noteText: {
    ...textStyles.body,
    color: colors.text,
    lineHeight: 24,
  },
  statsSection: {
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: spacing.component.radiusMD,
    padding: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    ...textStyles.h3,
    color: colors.primary,
    fontWeight: 'bold',
  },
  statLabel: {
    ...textStyles.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
});

export default ActivityDetailScreen;
