import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Animated,
} from 'react-native';

import { dataSource } from '../database/DataSource';
import { getSemanticColors, spacing, textStyles } from '../styles';
import { Activity, LongTermGoal, RootStackParamList, TagType } from '../types';
import { formatDateForDisplay, getSortedDateKeys, groupActivitiesByDate } from '../utils';
import {
  GradientBackground,
  Card,
  CardHeader,
  CardContent,
  FloatingActionButton,
  useTheme
} from '../components';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { theme } = useTheme();
  const colors = getSemanticColors(theme);

  const [activities, setActivities] = useState<Activity[]>([]);
  const [goals, setGoals] = useState<LongTermGoal[]>([]);
  const [balance, setBalance] = useState<number>(0);
  const [goalExpanded, setGoalExpanded] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;

  useEffect(() => {
    const updateData = () => {
      setActivities(dataSource.getActivities());
      setGoals(dataSource.getGoals());
      setBalance(dataSource.getBalance());
      setIsLoading(false);

      // Trigger entrance animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start();
    };

    // Initial load
    updateData();

    // Subscribe to data changes
    dataSource.addListener(updateData);

    return () => {
      dataSource.removeListener(updateData);
    };
  }, [fadeAnim, slideAnim]);

  const handleCandyPress = () => {
    navigation.navigate('NewActivity', { actType: TagType.GAIN });
  };

  const handleGoalPress = () => {
    const painTags = dataSource.getTagsByType(TagType.PAIN);
    if (painTags.length === 0) {
      Alert.alert(
        'No Tasks Available',
        'To create a goal, you need to create a task first',
        [{ text: 'OK' }]
      );
      return;
    }
    navigation.navigate('GoalView');
  };

  const handleActivityPress = (activity: Activity) => {
    navigation.navigate('ActivityDetail', { activity });
  };

  const handlePlusPress = () => {
    navigation.navigate('NewActivity', { actType: TagType.PAIN });
  };

  const renderGoalList = () => {
    if (goals.length === 0) return null;

    return (
      <Card variant="elevated" margin="medium" theme={theme}>
        <CardHeader>
          <TouchableOpacity
            style={styles.goalHeader}
            onPress={() => setGoalExpanded(!goalExpanded)}
          >
            <Ionicons
              name={goalExpanded ? 'chevron-down' : 'chevron-forward'}
              size={20}
              color={colors.textSecondary}
            />
            <Text style={[styles.goalHeaderText, { color: colors.textSecondary }]}>
              {goalExpanded ? 'Tap to collapse your goals' : 'Tap to view your hidden goals'}
            </Text>
          </TouchableOpacity>
        </CardHeader>

        {goalExpanded && (
          <CardContent>
            {goals.map((goal, index) => (
              <Animated.View
                key={goal.id}
                style={[
                  styles.goalItem,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <Text style={[styles.goalTitle, { color: colors.text }]}>
                  {goal.targetNo} x {dataSource.getTagNameById(goal.tagId)} + {goal.point}
                </Text>
                <Text style={[styles.goalDate, { color: colors.textSecondary }]}>
                  {goal.startTime.toLocaleDateString()} - {goal.endTime.toLocaleDateString()}
                </Text>
                <View style={styles.progressContainer}>
                  <View style={[styles.progressBar, { backgroundColor: colors.progressBackground }]}>
                    <Animated.View
                      style={[
                        styles.progressFill,
                        {
                          backgroundColor: colors.primary,
                          width: `${Math.min((goal.currentNo / goal.targetNo) * 100, 100)}%`
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                    {goal.currentNo} / {goal.targetNo}
                  </Text>
                </View>
              </Animated.View>
            ))}
          </CardContent>
        )}
      </Card>
    );
  };

  const renderActivityList = () => {
    if (activities.length === 0) {
      return (
        <Card variant="elevated" margin="medium" theme={theme}>
          <CardContent>
            <View style={styles.emptyState}>
              <Text style={[styles.emptyTitle, { color: colors.text }]}>No activities yet</Text>
              <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
                Click on the floating button to earn some points
              </Text>
              <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
                Click on the candy button to redeem your rewards
              </Text>
            </View>
          </CardContent>
        </Card>
      );
    }

    const groupedActivities = groupActivitiesByDate(activities);
    const sortedDates = getSortedDateKeys(groupedActivities);

    return (
      <View style={styles.activityList}>
        {sortedDates.map((date, dateIndex) => (
          <Card key={date} variant="elevated" margin="small" theme={theme}>
            <CardHeader>
              <Text style={[styles.dateHeader, { color: colors.textSecondary }]}>
                {formatDateForDisplay(new Date(date))}
              </Text>
            </CardHeader>
            <CardContent>
              {groupedActivities[date].map((activity, activityIndex) => (
                <Animated.View
                  key={activity.id}
                  style={{
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  }}
                >
                  <TouchableOpacity
                    style={[
                      styles.activityItem,
                      {
                        backgroundColor: activity.tagType === TagType.PAIN
                          ? colors.pain.background
                          : colors.gain.background,
                        borderColor: activity.tagType === TagType.PAIN
                          ? colors.pain.border
                          : colors.gain.border,
                      }
                    ]}
                    onPress={() => handleActivityPress(activity)}
                  >
                    <View style={styles.activityContent}>
                      <Text style={[styles.activityName, { color: colors.text }]}>
                        {activity.tagName}
                      </Text>
                      <Text style={[
                        styles.activityPoints,
                        {
                          color: activity.tagType === TagType.PAIN
                            ? colors.pain.text
                            : colors.gain.text
                        }
                      ]}>
                        {activity.tagType === TagType.PAIN ? '+' : '-'}{activity.point}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </CardContent>
          </Card>
        ))}
      </View>
    );
  };

  if (isLoading) {
    return (
      <GradientBackground variant="candy" theme={theme}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading...</Text>
        </View>
      </GradientBackground>
    );
  }

  return (
    <GradientBackground variant="candy" theme={theme}>
      {/* Header with candy and goal buttons */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }
        ]}
      >
        <TouchableOpacity style={styles.candyButton} onPress={handleCandyPress}>
          <Card variant="elevated" padding="small" margin="none" theme={theme}>
            <View style={styles.candyContainer}>
              <Text style={styles.candyEmoji}>🍭</Text>
              <Text style={[styles.balanceText, { color: colors.text }]}>{balance}</Text>
            </View>
          </Card>
        </TouchableOpacity>

        <TouchableOpacity style={styles.goalButton} onPress={handleGoalPress}>
          <Card variant="elevated" padding="small" margin="none" theme={theme}>
            <Ionicons name="flag-outline" size={24} color={colors.primary} />
          </Card>
        </TouchableOpacity>
      </Animated.View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderGoalList()}
        {renderActivityList()}
      </ScrollView>

      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={handlePlusPress}
        variant="candy"
        theme={theme}
      >
        <Ionicons name="add" size={28} color={colors.text} />
      </FloatingActionButton>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...textStyles.h4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.layout.screenPadding,
    paddingTop: spacing.component.safeAreaTop + spacing['4'],
    paddingBottom: spacing['4'],
  },
  candyButton: {
    flex: 1,
    alignItems: 'flex-start',
  },
  candyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing['4'],
    paddingVertical: spacing['2'],
  },
  candyEmoji: {
    fontSize: 32,
    marginRight: spacing['2'],
  },
  balanceText: {
    ...textStyles.h5,
    fontWeight: 'bold',
  },
  goalButton: {
    padding: spacing['2'],
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: spacing.layout.screenPadding,
    paddingBottom: spacing.component.tabBarHeight + spacing['20'],
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing['2'],
  },
  goalHeaderText: {
    ...textStyles.callout,
    marginLeft: spacing['2'],
  },
  goalItem: {
    marginBottom: spacing['4'],
    padding: spacing['4'],
    borderRadius: spacing.component.radiusMD,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  goalTitle: {
    ...textStyles.bodyMedium,
    marginBottom: spacing['1'],
  },
  goalDate: {
    ...textStyles.caption,
    marginBottom: spacing['3'],
  },
  progressContainer: {
    marginTop: spacing['2'],
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    ...textStyles.caption,
    textAlign: 'right',
    marginTop: spacing['1'],
  },
  activityList: {
    flex: 1,
  },
  dateHeader: {
    ...textStyles.labelLarge,
    marginBottom: spacing['2'],
  },
  activityItem: {
    borderRadius: spacing.component.radiusMD,
    marginBottom: spacing['2'],
    borderWidth: 1,
    overflow: 'hidden',
  },
  activityContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing['4'],
  },
  activityName: {
    ...textStyles.body,
    flex: 1,
  },
  activityPoints: {
    ...textStyles.bodyMedium,
    fontWeight: 'bold',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing['12'],
  },
  emptyTitle: {
    ...textStyles.h4,
    marginBottom: spacing['4'],
    textAlign: 'center',
  },
  emptySubtitle: {
    ...textStyles.body,
    textAlign: 'center',
    marginBottom: spacing['2'],
    lineHeight: 24,
  },
});

export default HomeScreen;
