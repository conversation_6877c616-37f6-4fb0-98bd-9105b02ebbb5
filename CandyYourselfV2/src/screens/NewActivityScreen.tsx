import { Ionicons } from '@expo/vector-icons';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { dataSource } from '../database/DataSource';
import { getSemanticColors, spacing, textStyles } from '../styles';
import { Activity, ActivityType, RootStackParamList, Tag, TagType } from '../types';
import { generateId } from '../utils';
import {
  GradientBackground,
  Card,
  CardHeader,
  CardContent,
  Button,
  Input,
  AnimatedScreen,
  CandyLoadingSpinner,
  useTheme
} from '../components';

type NewActivityScreenRouteProp = RouteProp<RootStackParamList, 'NewActivity'>;

const NewActivityScreen: React.FC = () => {
  const route = useRoute<NewActivityScreenRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const colors = getSemanticColors(theme);
  const { actType } = route.params;

  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [customTagName, setCustomTagName] = useState<string>('');
  const [points, setPoints] = useState<string>('');
  const [note, setNote] = useState<string>('');
  const [tags, setTags] = useState<Tag[]>([]);
  const [isCreatingNewTag, setIsCreatingNewTag] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  useEffect(() => {
    loadTags();
  }, [actType]);

  const loadTags = () => {
    const availableTags = dataSource.getTagsByType(actType);
    setTags(availableTags);
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!selectedTag && !isCreatingNewTag) {
      newErrors.tag = 'Please select or create a tag';
    }

    if (isCreatingNewTag && !customTagName.trim()) {
      newErrors.customTagName = 'Please enter a tag name';
    }

    if (!points.trim() || isNaN(Number(points)) || Number(points) <= 0) {
      newErrors.points = 'Please enter a valid point value';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateTag = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    const newTag: Tag = {
      id: generateId(),
      name: customTagName.trim(),
      type: actType,
      orderIndex: tags.length,
      createdAt: new Date(),
    };

    try {
      await dataSource.addTag(newTag);
      setTags([...tags, newTag]);
      setSelectedTag(newTag);
      setCustomTagName('');
      setIsCreatingNewTag(false);
      setErrors({});
    } catch (error) {
      Alert.alert('Error', 'Failed to create tag');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    let tagToUse = selectedTag;

    // If creating new tag, create it first
    if (isCreatingNewTag && customTagName.trim()) {
      const newTag: Tag = {
        id: generateId(),
        name: customTagName.trim(),
        type: actType,
        orderIndex: tags.length,
        createdAt: new Date(),
      };

      try {
        await dataSource.addTag(newTag);
        tagToUse = newTag;
      } catch (error) {
        Alert.alert('Error', 'Failed to create tag');
        setIsLoading(false);
        return;
      }
    }

    if (!tagToUse) {
      Alert.alert('Error', 'Please select a tag');
      return;
    }

    const newActivity: Activity = {
      id: generateId(),
      tagName: tagToUse.name,
      tagType: actType,
      note: note.trim() || undefined,
      point: Number(points),
      tagId: tagToUse.id,
      type: ActivityType.TASK,
      createdAt: new Date(),
    };

    try {
      await dataSource.addActivity(newActivity);
      Alert.alert('Success', 'Activity created successfully', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create activity');
    }
  };

  const renderTagItem = (tag: Tag) => (
    <TouchableOpacity
      key={tag.id}
      style={[
        styles.tagItem,
        selectedTag?.id === tag.id && styles.selectedTagItem,
        actType === TagType.PAIN ? commonStyles.activityPain : commonStyles.activityGain
      ]}
      onPress={() => {
        setSelectedTag(tag);
        setIsCreatingNewTag(false);
      }}
    >
      <Text style={[
        styles.tagText,
        selectedTag?.id === tag.id && styles.selectedTagText
      ]}>
        {tag.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <GradientBackground variant="candy" theme={theme}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <AnimatedScreen animationType="slide" duration={400}>
          <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
            {/* Header */}
            <Card variant="elevated" margin="medium" theme={theme}>
              <CardHeader>
                <View style={styles.header}>
                  <Text style={[styles.title, { color: colors.text }]}>
                    {actType === TagType.PAIN ? 'New Task' : 'New Reward'}
                  </Text>
                  <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
                    {actType === TagType.PAIN
                      ? 'Earn points by completing tasks'
                      : 'Spend points on rewards'
                    }
                  </Text>
                </View>
              </CardHeader>
            </Card>

            {/* Tag Selection */}
            <Card variant="elevated" margin="medium" theme={theme}>
              <CardHeader>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Select Category</Text>
              </CardHeader>
              <CardContent>
                <View style={styles.tagsContainer}>
                  {tags.map(renderTagItem)}

                  {/* Create new tag button */}
                  <TouchableOpacity
                    style={[
                      styles.tagItem,
                      styles.newTagItem,
                      { borderColor: colors.border },
                      isCreatingNewTag && { borderColor: colors.primary, backgroundColor: colors.primary + '20' }
                    ]}
                    onPress={() => {
                      setIsCreatingNewTag(true);
                      setSelectedTag(null);
                    }}
                  >
                    <Ionicons name="add" size={20} color={colors.textSecondary} />
                    <Text style={[styles.newTagText, { color: colors.textSecondary }]}>New Category</Text>
                  </TouchableOpacity>
                </View>

                {/* New tag input */}
                {isCreatingNewTag && (
                  <View style={styles.newTagInput}>
                    <Input
                      placeholder="Enter category name"
                      value={customTagName}
                      onChangeText={setCustomTagName}
                      error={errors.customTagName}
                      autoFocus
                      theme={theme}
                    />
                    <Button
                      title="Create"
                      onPress={handleCreateTag}
                      size="small"
                      loading={isLoading}
                      theme={theme}
                    />
                  </View>
                )}
              </CardContent>
            </Card>

            {/* Points Input */}
            <Card variant="elevated" margin="medium" theme={theme}>
              <CardContent>
                <Input
                  label="Points"
                  placeholder={actType === TagType.PAIN ? 'Points to earn' : 'Points to spend'}
                  value={points}
                  onChangeText={setPoints}
                  keyboardType="numeric"
                  error={errors.points}
                  theme={theme}
                />
              </CardContent>
            </Card>

            {/* Note Input */}
            <Card variant="elevated" margin="medium" theme={theme}>
              <CardContent>
                <Input
                  label="Note (Optional)"
                  placeholder="Add a note..."
                  value={note}
                  onChangeText={setNote}
                  multiline
                  numberOfLines={3}
                  theme={theme}
                />
              </CardContent>
            </Card>

            {/* Save Button */}
            <View style={styles.buttonContainer}>
              <Button
                title={actType === TagType.PAIN ? 'Complete Task' : 'Redeem Reward'}
                onPress={handleSave}
                variant={actType === TagType.PAIN ? 'primary' : 'secondary'}
                size="large"
                fullWidth
                loading={isLoading}
                icon={
                  <Ionicons
                    name={actType === TagType.PAIN ? 'checkmark-circle' : 'gift'}
                    size={20}
                    color={colors.textOnPrimary}
                  />
                }
                theme={theme}
              />
            </View>
          </ScrollView>
        </AnimatedScreen>
      </KeyboardAvoidingView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: spacing.layout.screenPadding,
    paddingBottom: spacing['20'],
  },
  header: {
    alignItems: 'center',
  },
  title: {
    ...textStyles.h4,
    textAlign: 'center',
    marginBottom: spacing['2'],
  },
  subtitle: {
    ...textStyles.body,
    textAlign: 'center',
  },
  sectionTitle: {
    ...textStyles.labelLarge,
    marginBottom: spacing['3'],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing['2'],
  },
  tagItem: {
    paddingHorizontal: spacing['4'],
    paddingVertical: spacing['2'],
    borderRadius: spacing.component.radiusMD,
    borderWidth: 1,
    marginRight: spacing['2'],
    marginBottom: spacing['2'],
  },
  tagText: {
    ...textStyles.body,
  },
  newTagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    borderStyle: 'dashed',
  },
  newTagText: {
    ...textStyles.body,
    marginLeft: spacing['2'],
  },
  newTagInput: {
    marginTop: spacing['4'],
    gap: spacing['3'],
  },
  buttonContainer: {
    marginTop: spacing['6'],
    marginBottom: spacing['4'],
  },
});

export default NewActivityScreen;
