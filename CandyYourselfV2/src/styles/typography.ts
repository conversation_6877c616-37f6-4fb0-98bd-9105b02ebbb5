import { Platform, TextStyle } from 'react-native';

// Modern font system with better hierarchy and readability

// Font families - Enhanced with better fallbacks
export const fontFamilies = {
  // Primary font family
  regular: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    default: 'System',
  }),
  medium: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto-Medium',
    default: 'System',
  }),
  bold: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto-Bold',
    default: 'System',
  }),
  light: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto-Light',
    default: 'System',
  }),

  // Display font family for headings
  display: Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    default: 'System',
  }),

  // Monospace for code/numbers
  mono: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    default: 'monospace',
  }),
} as const;

// Enhanced font sizes with better scale
export const fontSizes = {
  '2xs': 10,
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 28,
  '4xl': 32,
  '5xl': 40,
  '6xl': 48,
  '7xl': 56,
  '8xl': 64,
  '9xl': 72,
} as const;

// Improved line heights for better readability
export const lineHeights = {
  '2xs': 12,
  xs: 16,
  sm: 20,
  base: 24,
  lg: 28,
  xl: 32,
  '2xl': 36,
  '3xl': 40,
  '4xl': 44,
  '5xl': 48,
  '6xl': 56,
  '7xl': 64,
  '8xl': 72,
  '9xl': 80,
} as const;

// Enhanced font weights
export const fontWeights = {
  thin: '100' as const,
  extralight: '200' as const,
  light: '300' as const,
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  extrabold: '800' as const,
  black: '900' as const,
} as const;

// Letter spacing values
export const letterSpacing = {
  tighter: -0.5,
  tight: -0.25,
  normal: 0,
  wide: 0.25,
  wider: 0.5,
  widest: 1,
} as const;

// Legacy typography object for backward compatibility
export const typography = {
  fontFamily: {
    regular: fontFamilies.regular,
    medium: fontFamilies.medium,
    bold: fontFamilies.bold,
  },
  fontSize: {
    xs: fontSizes.xs,
    sm: fontSizes.sm,
    base: fontSizes.base,
    lg: fontSizes.lg,
    xl: fontSizes.xl,
    '2xl': fontSizes['2xl'],
    '3xl': fontSizes['3xl'],
    '4xl': fontSizes['4xl'],
    '5xl': fontSizes['5xl'],
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
  fontWeight: {
    light: fontWeights.light,
    normal: fontWeights.normal,
    medium: fontWeights.medium,
    semibold: fontWeights.semibold,
    bold: fontWeights.bold,
    extrabold: fontWeights.extrabold,
  },
} as const;

// Modern text styles with improved hierarchy
export const textStyles = {
  // Display styles - For hero sections and major headings
  display1: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['9xl'],
    lineHeight: lineHeights['9xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  display2: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['8xl'],
    lineHeight: lineHeights['8xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  display3: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['7xl'],
    lineHeight: lineHeights['7xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  // Heading styles - Improved hierarchy
  h1: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['6xl'],
    lineHeight: lineHeights['6xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  h2: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['5xl'],
    lineHeight: lineHeights['5xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  h3: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['4xl'],
    lineHeight: lineHeights['4xl'],
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  h4: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes['3xl'],
    lineHeight: lineHeights['3xl'],
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  h5: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes['2xl'],
    lineHeight: lineHeights['2xl'],
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  h6: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.xl,
    lineHeight: lineHeights.xl,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  // Body text styles - Enhanced readability
  bodyLarge: {
    fontFamily: fontFamilies.regular,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  body: {
    fontFamily: fontFamilies.regular,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  bodyMedium: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  bodySmall: {
    fontFamily: fontFamilies.regular,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  // Label styles - For form labels and UI elements
  labelLarge: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wide,
  } as TextStyle,

  label: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wide,
  } as TextStyle,

  labelSmall: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wider,
  } as TextStyle,

  // Utility styles
  caption: {
    fontFamily: fontFamilies.regular,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  captionMedium: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wide,
  } as TextStyle,

  overline: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes['2xs'],
    lineHeight: lineHeights['2xs'],
    fontWeight: fontWeights.semibold,
    textTransform: 'uppercase',
    letterSpacing: letterSpacing.widest,
  } as TextStyle,

  // Button styles
  buttonLarge: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.wide,
  } as TextStyle,

  button: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.wide,
  } as TextStyle,

  buttonSmall: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.wider,
  } as TextStyle,

  // Specialized styles
  callout: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  footnote: {
    fontFamily: fontFamilies.regular,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  // Legacy styles for backward compatibility
  title: {
    fontFamily: fontFamilies.display,
    fontSize: fontSizes['2xl'],
    lineHeight: lineHeights['2xl'],
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  } as TextStyle,

  subtitle: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  navTitle: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  tabLabel: {
    fontFamily: fontFamilies.medium,
    fontSize: fontSizes.xs,
    lineHeight: lineHeights.xs,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.wider,
  } as TextStyle,

  // Monospace styles for numbers/code
  mono: {
    fontFamily: fontFamilies.mono,
    fontSize: fontSizes.base,
    lineHeight: lineHeights.base,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  monoLarge: {
    fontFamily: fontFamilies.mono,
    fontSize: fontSizes.lg,
    lineHeight: lineHeights.lg,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,

  monoSmall: {
    fontFamily: fontFamilies.mono,
    fontSize: fontSizes.sm,
    lineHeight: lineHeights.sm,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  } as TextStyle,
} as const;

// Text style utilities
export const getTextStyle = (variant: keyof typeof textStyles) => textStyles[variant];

// Responsive text scaling (for accessibility)
export const getScaledTextStyle = (variant: keyof typeof textStyles, scale: number = 1) => {
  const baseStyle = textStyles[variant];
  return {
    ...baseStyle,
    fontSize: baseStyle.fontSize * scale,
    lineHeight: baseStyle.lineHeight * scale,
  };
};
