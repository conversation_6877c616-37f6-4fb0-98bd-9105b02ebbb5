// Export all style modules
export * from './colors';
export * from './typography';
export * from './spacing';

import { StyleSheet } from 'react-native';
import { colors } from './colors';
import { textStyles } from './typography';
import { spacing } from './spacing';

// Common styles used throughout the app
export const commonStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  safeContainer: {
    flex: 1,
    backgroundColor: colors.background,
    paddingTop: spacing.component.safeAreaTop,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: colors.background,
    padding: spacing.layout.screenPadding,
  },
  
  // Flex utilities
  flex1: {
    flex: 1,
  },
  flexRow: {
    flexDirection: 'row',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  flexCenter: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexBetween: {
    justifyContent: 'space-between',
  },
  flexAround: {
    justifyContent: 'space-around',
  },
  flexEvenly: {
    justifyContent: 'space-evenly',
  },
  alignCenter: {
    alignItems: 'center',
  },
  alignStart: {
    alignItems: 'flex-start',
  },
  alignEnd: {
    alignItems: 'flex-end',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  justifyStart: {
    justifyContent: 'flex-start',
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  
  // Text styles
  textCenter: {
    textAlign: 'center',
  },
  textLeft: {
    textAlign: 'left',
  },
  textRight: {
    textAlign: 'right',
  },
  
  // Card styles
  card: {
    backgroundColor: colors.background,
    borderRadius: spacing.component.radiusMD,
    padding: spacing.component.cardPadding,
    margin: spacing.component.cardMargin,
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  // Button styles
  button: {
    paddingVertical: spacing.component.buttonPaddingVertical,
    paddingHorizontal: spacing.component.buttonPaddingHorizontal,
    borderRadius: spacing.component.radiusMD,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: colors.buttonPrimary,
  },
  buttonSecondary: {
    backgroundColor: colors.buttonSecondary,
  },
  buttonSuccess: {
    backgroundColor: colors.buttonSuccess,
  },
  buttonDanger: {
    backgroundColor: colors.buttonDanger,
  },
  buttonText: {
    ...textStyles.button,
    color: colors.textLight,
  },
  buttonTextSecondary: {
    ...textStyles.button,
    color: colors.text,
  },
  
  // Input styles
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: spacing.component.radiusMD,
    paddingVertical: spacing.component.inputPaddingVertical,
    paddingHorizontal: spacing.component.inputPaddingHorizontal,
    fontSize: textStyles.body.fontSize,
    backgroundColor: colors.background,
  },
  inputFocused: {
    borderColor: colors.primary,
  },
  inputError: {
    borderColor: colors.error,
  },
  
  // List styles
  listItem: {
    padding: spacing.component.listItemPadding,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  listItemLast: {
    borderBottomWidth: 0,
  },
  
  // Shadow styles
  shadowSmall: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  shadowMedium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  shadowLarge: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  
  // Border styles
  border: {
    borderWidth: 1,
    borderColor: colors.border,
  },
  borderTop: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  borderLeft: {
    borderLeftWidth: 1,
    borderLeftColor: colors.border,
  },
  borderRight: {
    borderRightWidth: 1,
    borderRightColor: colors.border,
  },
  
  // Background styles
  backgroundPrimary: {
    backgroundColor: colors.primary,
  },
  backgroundSecondary: {
    backgroundColor: colors.backgroundSecondary,
  },
  backgroundTertiary: {
    backgroundColor: colors.backgroundTertiary,
  },
  
  // Overlay styles
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.overlay,
  },
  overlayLight: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: colors.overlayLight,
  },
  
  // Activity specific styles
  activityPain: {
    backgroundColor: colors.pain.background,
    borderColor: colors.pain.border,
    borderWidth: 1,
  },
  activityGain: {
    backgroundColor: colors.gain.background,
    borderColor: colors.gain.border,
    borderWidth: 1,
  },
  
  // Progress bar styles
  progressBar: {
    height: 8,
    backgroundColor: colors.progressBackground,
    borderRadius: spacing.component.radiusXS,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.progressFill,
  },
});
